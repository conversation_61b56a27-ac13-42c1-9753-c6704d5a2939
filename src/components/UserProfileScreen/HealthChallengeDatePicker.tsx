import moment from 'moment-timezone';
import React, { useEffect, useMemo, useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Alert,
  Modal,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import AppleHealthKit from 'react-native-health';
import { FontStyle, FontWeight, Skia } from '@shopify/react-native-skia';
import RNFS from 'react-native-fs';
import { Buffer } from 'buffer';
import axios from 'axios';
import { logoBase64, logoBase641 } from '../../utils/Utils';
import {
  initialize,
  requestPermission,
  getGrantedPermissions,
  readRecords,
} from 'react-native-health-connect';
import Loader from '../Common/Loader';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { set } from 'react-native-reanimated';
import {useAppSelector} from '../../redux/Store';
import { ExerciseType } from 'react-native-health-connect';



const stepBgImages = [
  'https://drive.google.com/uc?export=download&id=1kPjex_1vNNWUYsDvIOeRmdyGGdfGr45x',
  'https://drive.google.com/uc?export=download&id=1PR5Fs7d-1bnGrPQD6hmxX8q_XffvIaKI',
  'https://drive.google.com/uc?export=download&id=1mENSYbLe7hDIqlmFBrvlP2yVABCVv14F',
  'https://drive.google.com/uc?export=download&id=1CSLNxowxi57Vvu6iYA7dmnraFZOgrlmy',
  'https://drive.google.com/uc?export=download&id=1g6AKrch4-Zla-sbOKiwzw992vXwa4LuL',
];

const calorieBgImages = [
 'https://drive.google.com/uc?export=download&id=1gvXWfWBYPAp_Iy3ZF6TD4BkhquDYJod2',
  'https://drive.google.com/uc?export=download&id=1WP61cQh2sRFVEhK_IHTpPuC2rTxALjFE',
  'https://drive.google.com/uc?export=download&id=1WmGKuDTsv3cGdcrzXrEDMZ6vqvssrGuZ',
  'https://drive.google.com/uc?export=download&id=16574txTVQF-NLEwMmlKKlqxt5_oQTDz1',
  'https://drive.google.com/uc?export=download&id=1QRBVWg0GfaZvfIUZSLFG6HNqC1OI-Pa9',
]; 

const cyclingBgImages = [
  'https://drive.google.com/uc?export=download&id=1Yr6fJBih9aZkBJqyUkzfdkS1c0yk6d9b',
  'https://drive.google.com/uc?export=download&id=1EjpyhML61FRgv0dhjPDwSb0pQKfDV2Cg',
  'https://drive.google.com/uc?export=download&id=1sQHfQ5HpdHfPN5qPU88_PwxO1Mz7sjnJ',
  'https://drive.google.com/uc?export=download&id=1XRXMqOcjyXzQIsh5tn7FLu32nZI-hAGz',
];

const swimmingBgImages = [
  'https://drive.google.com/uc?export=download&id=1kPjex_1vNNWUYsDvIOeRmdyGGdfGr45x', // Placeholder - you can replace with swimming-specific images
  'https://drive.google.com/uc?export=download&id=1PR5Fs7d-1bnGrPQD6hmxX8q_XffvIaKI',
  'https://drive.google.com/uc?export=download&id=1mENSYbLe7hDIqlmFBrvlP2yVABCVv14F',
  'https://drive.google.com/uc?export=download&id=1CSLNxowxi57Vvu6iYA7dmnraFZOgrlmy',
];

const waterSportsBgImages = [
  'https://drive.google.com/uc?export=download&id=1g6AKrch4-Zla-sbOKiwzw992vXwa4LuL', // Placeholder - you can replace with water sports-specific images
  'https://drive.google.com/uc?export=download&id=1Yr6fJBih9aZkBJqyUkzfdkS1c0yk6d9b',
  'https://drive.google.com/uc?export=download&id=1EjpyhML61FRgv0dhjPDwSb0pQKfDV2Cg',
  'https://drive.google.com/uc?export=download&id=1sQHfQ5HpdHfPN5qPU88_PwxO1Mz7sjnJ',
];

const getRandomItem = (array: any) => {
  if (!array || array.length === 0) {
    throw new Error('Array is empty or undefined');
  }
  const randomIndex = Math.floor(Math.random() * array.length);
  return array[randomIndex];
};

// Helper function to check if Health Connect is available
const isHealthConnectAvailable = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    return false;
  }

  try {
    await initialize();
    return true;
  } catch (error) {
    console.log('Health Connect not available:', error);
    return false;
  }
};

const HealthChallengeDatePicker = ({
  onImageGenerate,
  config,
  onDataUpdate,
  disabled = false,
}: any) => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [openStart, setOpenStart] = useState(false);
  const [permissionLoading, setPermissionLoading] = useState(false);
  const [openEnd, setOpenEnd] = useState(false);
  const unit = config?.unit ?? useAppSelector(state => state.auth.selectedUnit);
  const [stepData, setStepData] = useState<
    Array<{ date: string; steps: number }>
  >([]);
  const [calorieData, setCalorieData] = useState<
    Array<{ date: string; calorie: number }>
  >([]);
  const [distanceData, setDistanceData] = useState<
    Array<{ date: string; meter: number }>
  >([]);
  const [cyclingDistanceData, setCyclingDistanceData] = useState<
  Array<{ date: string; meter: number }>
>([]);
const [flightsClimbedData, setFlightsClmibedData] = useState<
Array<{ date: string; floor: number }>
>([]);
const [basalEnergyData, setBasalEnergyData] = useState<
Array<{ date: string; calorie: number }>
>([]);
const [activitySummaryData, setActivitySummaryData] = useState<
Array<{ date: string; act: number }>
>([]);
const [swimmingData, setSwimmingData] = useState<
Array<{ date: string; distance: number }>
>([]);
const [paddlingData, setPaddlingData] = useState<
Array<{ date: string; distance: number; calories: number }>
>([]);
const [waterWorkoutData, setWaterWorkoutData] = useState<
Array<{ date: string; distance: number; calories: number; activityName: string }>
>([]);

  const [dataFetched, setDataFetched] = useState(false); // Track when data is fetched
  const ranOnceRef = useRef(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isHealthConnectInitialized, setIsHealthConnectInitialized] = useState(false);
  const healthPermission: any = {
    permissions: {
      read: [
        AppleHealthKit.Constants.Permissions.StepCount,
        AppleHealthKit.Constants.Permissions.Steps,
        AppleHealthKit.Constants.Permissions.DistanceWalkingRunning,
        AppleHealthKit.Constants.Permissions.DistanceCycling,
        AppleHealthKit.Constants.Permissions.DistanceSwimming,
        AppleHealthKit.Constants.Permissions.ActiveEnergyBurned,
        AppleHealthKit.Constants.Permissions.Workout,
        AppleHealthKit.Constants.Permissions.FlightsClimbed,
        AppleHealthKit.Constants.Permissions.BasalEnergyBurned,
        AppleHealthKit.Constants.Permissions.ActivitySummary,



      ],
      write: [],
    },
  };

  useEffect(() => {
    checkHealthConnection();
  }, []);

  const checkHealthConnection = async () => {
    if (Platform.OS === 'ios') {
      AppleHealthKit.getAuthStatus(healthPermission, (err, result) => {
        if (err) {
          console.log('Error getting Apple Health auth status:', err);
          return;
        }
        const grantedResult = result.permissions.read.filter(
          item => item !== 0,
        ).length;
        if (grantedResult > 0) {
          setIsConnected(true);

       // Trigger permission prompt for FlightsClimbed
          AppleHealthKit.getFlightsClimbed(
            {
              date: new Date().toISOString(),
              includeManuallyAdded: true,
            },
            (err, result) => {
              if (err) {
                console.log('Error fetching flights climbed (for permission):', err);
              } else {
                console.log('Flights climbed (just to trigger permission):', result?.value);
              }
            }
          );

          AppleHealthKit.getBasalEnergyBurned(
            {
              startDate: new Date().toISOString(),
              endDate: new Date().toISOString(),
              includeManuallyAdded: true,
            },
            (err, result) => {
              if (err) {
                console.log('Error fetching Basal energy Burned (for permission):', err);
              } else {
                console.log('Basal energy Burned (just to trigger permission):', result);
              }
            }
          );
          AppleHealthKit.getActivitySummary(
            {
              startDate: new Date().toISOString(),
              endDate: new Date().toISOString(),
              includeManuallyAdded: true,
            },
            (err, result) => {
              if (err) {
                console.log('Error fetching Activity summary (for permission):', err);
              } else {
                console.log('Activity summary (just to trigger permission):', result);
              }
            }
          );
         
          // Fetch initial data for iOS
          const data = {
            start: moment().startOf('day').toDate(),
            end: moment().endOf('day').toDate(),
          };
          fetchStepData(data);
          fetchCaloriesData(data);
          fetchDistanceData(data);
          fetchCyclingDistanceData(data);
          fetchSwimmingData(data);
          fetchWaterSportsData(data);

        } else {
          setIsConnected(false);
        }
      });
    } else {
      try {
        console.log('Initializing Health Connect...');
        await initialize();
        setIsHealthConnectInitialized(true);
        console.log('Health Connect initialized successfully');

        const permissions = await getGrantedPermissions();
        console.log('Granted permissions:', permissions);

        if (permissions.length > 0) {
          setIsConnected(true);
          // Fetch initial data for Android only if we have permissions
          const data = {
            start: moment().startOf('day').toDate(),
            end: moment().endOf('day').toDate(),
          };
          fetchStepData(data);
          fetchCaloriesData(data);
          fetchDistanceData(data);
          fetchCyclingDistanceData(data);
          fetchSwimmingData(data);
          fetchWaterSportsData(data);


        } else {
          setIsConnected(false);
        }
      } catch (error) {
        console.log('Health Connect initialization failed:', error);
        setIsHealthConnectInitialized(false);
        setIsConnected(false);

        // Check if it's a specific initialization error
        if (error && typeof error === 'object' && 'message' in error) {
          const errorMessage = (error as Error).message;
          if (errorMessage.includes('not initialized') || errorMessage.includes('not available')) {
            console.log('Health Connect app may not be installed or available');
          }
        }
      }
    }
  };

  // Compute totals before using them in useEffect
  const totalSteps = useMemo(() => {
    const total = stepData.reduce((sum, entry) => sum + entry.steps, 0);
    return Math.round(total );
  }, [stepData]);

  const totalCalorie = useMemo(() => {
    const total = calorieData.reduce((sum, entry) => sum + entry.calorie, 0);
    return Math.round(total * 100) / 100;
  }, [calorieData]);

  // const totalDistance = useMemo(() => {
  //   const total = distanceData.reduce((sum, entry) => sum + entry.meter, 0);
  //   return Math.round(total * 100) / 100;
  // }, [distanceData]);
  // const totalCyclingDistance = useMemo(() => {
  //   const total = cyclingDistanceData.reduce((sum, entry) => sum + entry.meter, 0);
  //   return Math.round(total * 100) / 100;
  // }, [cyclingDistanceData]);

  const totalDistance = useMemo(() => {
    const total = distanceData.reduce((sum, entry) => sum + entry.meter, 0);
    return unit === 'IMPERIAL'
      ? Math.round((total * 0.000621371) * 100) / 100 // in miles
      : Math.round((total / 1000) * 100) / 100; // in kilometers
  }, [distanceData, unit]);
  
  const totalCyclingDistance = useMemo(() => {
    const total = cyclingDistanceData.reduce((sum, entry) => sum + entry.meter, 0);
    return unit === 'IMPERIAL'
      ? Math.round((total * 0.000621371) * 100) / 100 // in miles
      : Math.round((total / 1000) * 100) / 100; // in kilometers
  }, [cyclingDistanceData, unit]);

  const totalSwimmingDistance = useMemo(() => {
    const total = swimmingData.reduce((sum, entry) => sum + entry.distance, 0);
    return Math.round(total * 100) / 100;
  }, [swimmingData]);

  const totalWaterSportsCalories = useMemo(() => {
    const total = waterWorkoutData.reduce((sum, entry) => sum + entry.calories, 0);
    return Math.round(total * 100) / 100;
  }, [waterWorkoutData]);

  const totalWaterSportsDistance = useMemo(() => {
    const total = waterWorkoutData.reduce((sum, entry) => sum + entry.distance, 0);
    return Math.round(total * 100) / 100;
  }, [waterWorkoutData]);

  // Call onDateSelect when both startDate and endDate are selected
  useEffect(() => {
    if (startDate && endDate) {
      onDateSelect({ start: startDate, end: endDate });
    }
  }, [startDate, endDate, config.importDataType]);

  // Call generateImage exactly once when data is fetched, with guard
  useEffect(() => {
    if (
      dataFetched &&
      startDate !== null &&
      endDate !== null &&
      !ranOnceRef.current
    ) {
      ranOnceRef.current = true;

      generateImage({
        start: startDate,
        end: endDate,
      });
      console.log(
        'Data fetched and image generated',
        startDate,
        endDate);
      console.log('Swimming data:', swimmingData);
      console.log('Water sports data:', waterWorkoutData);
      if (onDataUpdate) {
        onDataUpdate({
          steps: totalSteps,
          calories: totalCalorie,
          distance: totalDistance,
          cyclingDistance: totalCyclingDistance,
          swimmingDistance: totalSwimmingDistance,
          waterSportsCalories: totalWaterSportsCalories,
          waterSportsDistance: totalWaterSportsDistance,
          waterWorkouts: waterWorkoutData,
        });
      }

      setDataFetched(false);
      setTimeout(() => {
        ranOnceRef.current = false;
      }, 0);
    }
  }, [dataFetched, startDate, endDate]);

  const base64ToUint8Array = (base64: string): Uint8Array => {
    const binaryString = Buffer.from(base64, 'base64').toString('binary');
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  };

  const generateImage = async (data: { start: Date; end: Date }) => {
    console.log('🎨 Starting image generation on platform:', Platform.OS);
    const width = 300;
    const height = 300;
    const surface = Skia.Surface.MakeOffscreen(width, height);

    if (!surface) {
      console.error('Failed to create Skia surface');
      return;
    }
    const canvas = surface.getCanvas();

    if (!canvas) {
      console.error('Failed to get canvas from surface');
      return;
    }

    // Load remote background image
    const getBackgroundImages = () => {
      const dataType = config.importDataType.toLowerCase();
      if (dataType === 'steps' || dataType === 'distance') {
        return stepBgImages;
      } else if (dataType === 'calories') {
        return calorieBgImages;
      } else if (dataType === 'cycling' || dataType === 'cyclingdistance') {
        return cyclingBgImages;
      } else if (dataType === 'swimming' || dataType === 'swimmingdistance') {
        return swimmingBgImages;
      } else if (dataType === 'watersports' || dataType === 'paddling') {
        return waterSportsBgImages;
      } else {
        return cyclingBgImages; // default fallback
      }
    };

    const imageUrl = getRandomItem(getBackgroundImages());
    console.log('Attempting to fetch image from:', imageUrl);

    try {
      const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
      const arrayBuffer = response.data;
      const base64String = Buffer.from(arrayBuffer).toString('base64');
      const bytes = Buffer.from(base64String, 'base64');
      const skiaData = Skia.Data.fromBytes(new Uint8Array(bytes));
      const backgroundImage = Skia.Image.MakeImageFromEncoded(skiaData);

      if (!backgroundImage) {
        throw new Error('Failed to create Skia image from encoded data');
      }

      const bgPaint = Skia.Paint();
      canvas.drawImageRect(
        backgroundImage,
        Skia.XYWHRect(0, 0, backgroundImage.width(), backgroundImage.height()),
        Skia.XYWHRect(0, 0, width, height),
        bgPaint,
      );
    } catch (e) {
      console.error('Failed to load background image:', e);
      const bgPaint = Skia.Paint();
      bgPaint.setColor(Skia.Color('#C3E7F5'));
      canvas.drawRect(Skia.XYWHRect(0, 0, width, height), bgPaint);
    }

    // Add semi-transparent overlay
    const overlayPaint = Skia.Paint();
    overlayPaint.setColor(Skia.Color('rgba(0, 0, 0, 0.5)'));
    canvas.drawRect(Skia.XYWHRect(0, 0, width, height), overlayPaint);

    try {
      const imageBytes = base64ToUint8Array(logoBase64);
      const skiaData = Skia.Data.fromBytes(imageBytes);
      const logoImage = Skia.Image.MakeImageFromEncoded(skiaData);
      console.log(logoImage, 'logoImagelogoImage');

      if (logoImage) {
        canvas.drawImageRect(
          logoImage,
          Skia.XYWHRect(0, 0, logoImage.width(), logoImage.height()),
          Skia.XYWHRect(10, 20, 54, 54),
          Skia.Paint(),
        );
      }

      const imageBytes1 = base64ToUint8Array(logoBase641);
      const skiaData1 = Skia.Data.fromBytes(imageBytes1);
      const logoImage1 = Skia.Image.MakeImageFromEncoded(skiaData1);
      console.log(logoImage1, 'logoImagelogoImage');

      if (logoImage1) {
        canvas.drawImageRect(
          logoImage1,
          Skia.XYWHRect(0, 0, logoImage1.width(), logoImage1.height()),
          Skia.XYWHRect(30, 90, 21, 21),
          Skia.Paint(),
        );
      }
    } catch (error) {
      console.log(error);
    }

    // Text
    const paint = Skia.Paint();
    paint.setColor(Skia.Color('#FFFFFF'));
    paint.setAntiAlias(true);

    // Additional paint settings for better text rendering on Android
    paint.setStyle(0); // Fill style
    paint.setStrokeWidth(1);

    console.log('Setting up text rendering with paint color: #FFFFFF');

    // Simple fallback text drawing function for Android
    const drawTextSimple = (text: any, x: any, y: any, size = 16) => {
      try {
        console.log(`Attempting simple text draw: "${text}" at (${x}, ${y})`);

        // Create a very basic font
        const fontMgr = Skia.FontMgr.System();
        const typeface = fontMgr.matchFamilyStyle('', {
          weight: FontWeight.Normal,
        });

        if (typeface) {
          const font = Skia.Font(typeface, size);
          canvas.drawText(text, x, y, paint, font);
          console.log(`Simple text draw successful: "${text}"`);
          return true;
        }
        return false;
      } catch (error) {
        console.log('Simple text draw failed:', error);
        return false;
      }
    };

    // Alternative text drawing using TextBlob (might work better on some Android devices)
    const drawTextBlob = (text: any, x: any, y: any, size = 16) => {
      try {
        console.log(`Attempting TextBlob draw: "${text}" at (${x}, ${y})`);

        const fontMgr = Skia.FontMgr.System();
        const typeface = fontMgr.matchFamilyStyle('Arial', {
          weight: FontWeight.Normal,
        });

        if (typeface) {
          const font = Skia.Font(typeface, size);
          const textBlob = Skia.TextBlob.MakeFromText(text, font);
          if (textBlob) {
            canvas.drawTextBlob(textBlob, x, y, paint);
            console.log(`TextBlob draw successful: "${text}"`);
            return true;
          }
        }
        return false;
      } catch (error) {
        console.log('TextBlob draw failed:', error);
        return false;
      }
    };

    const drawText = (text: any, x: any, y: any, size = 16, isBold = false) => {
      try {
        // Try multiple font loading approaches for better Android compatibility
        let font;

        // Approach 1: Try system font manager with fallbacks
        try {
          const fontMgr = Skia.FontMgr.System();
          let typeface;

          // Try different font families that are more likely to exist on Android
          const fontFamilies = ['Arial', 'sans-serif', 'Roboto', 'Helvetica', 'default'];

          for (const fontFamily of fontFamilies) {
            typeface = fontMgr.matchFamilyStyle(fontFamily, {
              weight: isBold ? FontWeight.Bold : FontWeight.Normal,
            });
            if (typeface) {
              console.log(`Successfully loaded font: ${fontFamily}`);
              break;
            }
          }

          if (typeface) {
            font = Skia.Font(typeface, size);
          }
        } catch (fontMgrError) {
          console.log('FontMgr approach failed:', fontMgrError);
        }

        // Approach 2: Fallback to default font if system font manager fails
        if (!font) {
          try {
            // Create font without specifying typeface (uses system default)
            const fontMgr = Skia.FontMgr.System();
            const defaultTypeface = fontMgr.matchFamilyStyle('', {
              weight: FontWeight.Normal,
            });
            if (defaultTypeface) {
              font = Skia.Font(defaultTypeface, size);
              console.log('Using default system font');
            }
          } catch (defaultFontError) {
            console.log('Default font approach failed:', defaultFontError);
          }
        }

        // Approach 3: Last resort - create a basic font with monospace
        if (!font) {
          try {
            const fontMgr = Skia.FontMgr.System();
            const typeface = fontMgr.matchFamilyStyle('monospace', {
              weight: FontWeight.Normal,
            });
            if (typeface) {
              font = Skia.Font(typeface, size);
              console.log('Using monospace system font');
            }
          } catch (basicFontError) {
            console.error('All font loading approaches failed:', basicFontError);
          }
        }

        // Approach 4: Ultimate fallback - try to create any font
        if (!font) {
          try {
            // Try to create a font with minimal parameters
            const fontMgr = Skia.FontMgr.System();
            const typeface = fontMgr.matchFamilyStyle('serif', {
              weight: FontWeight.Normal,
            }) || fontMgr.matchFamilyStyle('sans-serif', {
              weight: FontWeight.Normal,
            });

            if (typeface) {
              font = Skia.Font(typeface, size);
              console.log('Using fallback serif/sans-serif font');
            } else {
              console.error('No suitable typeface found');
              return;
            }
          } catch (ultimateFallbackError) {
            console.error('Ultimate fallback font creation failed:', ultimateFallbackError);
            return;
          }
        }

        if (font) {
          console.log(`Drawing text: "${text}" at position (${x}, ${y}) with size ${size}`);
          canvas.drawText(text, x, y, paint, font);
          console.log(`Successfully drew text: "${text}"`);
        } else {
          console.error('Failed to create font for text, trying fallback approaches:', text);

          // Try the simple fallback approach
          let success = drawTextSimple(text, x, y, size);

          // If simple approach fails, try TextBlob
          if (!success) {
            console.log('Simple approach failed, trying TextBlob...');
            success = drawTextBlob(text, x, y, size);
          }

          if (!success) {
            console.error('All text drawing approaches failed for:', text);
          }
        }
      } catch (error) {
        console.error('Error in drawText function:', error);
      }
    };



    console.log('Starting to draw text elements...');
    console.log('Canvas available:', !!canvas);
    console.log('Paint available:', !!paint);

    drawText('Fitness Activity', 80, 50, 24, true);

    const dateRange = `${moment(data.start).format('DD-MM-YYYY')} - ${moment(data.end).format('DD-MM-YYYY')}`;
    console.log('Drawing date range:', dateRange);
    drawText(dateRange, 80, 70, 14);

    const healthAppName = Platform.OS === 'ios' ?'Apple Health'
      : 'Google Connect';
    console.log('Drawing health app name:', healthAppName);
    drawText(healthAppName, 80, 105, 14);
    console.log('Drawing data-specific text. Data type:', config.importDataType.toLowerCase());

    if (config.importDataType.toLowerCase() === 'steps') {
      console.log('Drawing steps data:', totalSteps);
      drawText(`Steps`, 20, 200, 35, true);
      drawText(`${totalSteps}`, 20, 245, 35, true);
      drawText(`Napoz`, 120, 290, 20, true);

    } else if (config.importDataType.toLowerCase() === 'calories') {
      console.log('Drawing calories data:', totalCalorie);
      drawText(`Calories`, 20, 200, 35, true);
      drawText(`${totalCalorie} Kcal`, 20, 245, 35, true);
      drawText(`Napoz`, 120, 290, 20, true);

    // } else if (config.importDataType.toLowerCase() === 'distance') {
    //   console.log('Drawing distance data:', totalDistance);
    //   drawText(`Distance`, 20, 200, 35, true);
    //   drawText(`${totalDistance} meter`, 20, 245, 35, true);
    // } else {
    //   console.log('Drawing cycling distance data:', totalCyclingDistance);
    //   drawText(`Cycling Distance`, 20, 200, 35, true);
    //   drawText(`${totalCyclingDistance} meter`, 20, 245, 35, true);
    // }
    } else if (config.importDataType.toLowerCase() === 'distance') {
      const distanceUnit = unit === 'IMPERIAL' ? 'miles' : 'km';
      console.log('Drawing distance data:', totalDistance);
      drawText(`Distance`, 20, 200, 35, true);
      drawText(`${totalDistance} ${distanceUnit}`, 20, 245, 35, true);
      drawText(`Napoz`, 120, 290, 20, true);

    } else if (config.importDataType.toLowerCase() === 'swimming' || config.importDataType.toLowerCase() === 'swimmingdistance') {
      const swimmingUnit = unit === 'IMPERIAL' ? 'miles' : 'km';
      console.log('Drawing swimming distance data:', totalSwimmingDistance);
      drawText(`Swimming`, 20, 155, 35, true);
      drawText(`Distance`, 20, 200, 35, true);
      drawText(`${totalSwimmingDistance} ${swimmingUnit}`, 20, 245, 35, true);
      drawText(`Napoz`, 120, 290, 20, true);
    } else if (config.importDataType.toLowerCase() === 'watersports' || config.importDataType.toLowerCase() === 'paddling') {
      console.log('Drawing water sports data:', totalWaterSportsDistance, totalWaterSportsCalories);
      drawText(`Water Sports`, 20, 155, 35, true);
      drawText(`${totalWaterSportsDistance} ${unit === 'IMPERIAL' ? 'mi' : 'km'}`, 20, 200, 35, true);
      drawText(`${totalWaterSportsCalories} Cal`, 20, 245, 35, true);
      drawText(`Napoz`, 120, 290, 20, true);
    } else {
      const cyclingUnit = unit === 'IMPERIAL' ? 'miles' : 'km';
      console.log('Drawing cycling distance data:', totalCyclingDistance);
      drawText(`Cycling `, 20, 155, 35, true);
      drawText(`Distance`, 20, 200, 35, true);
      drawText(`${totalCyclingDistance} ${cyclingUnit}`, 20, 245, 35, true);
      drawText(`Napoz`, 120, 290, 20, true);

    }

    const image = surface.makeImageSnapshot();
    if (!image) {
      console.error('Failed to create image snapshot');
      return;
    }

    try {
      const bytes = image.encodeToBytes();
      if (!bytes) {
        console.error('Failed to encode image');
        return;
      }

      const path = `${RNFS.TemporaryDirectoryPath
        }/health-challenge-${Date.now()}.png`;
      const base64String = Buffer.from(bytes).toString('base64');
      await RNFS.writeFile(path, base64String, 'base64');
      console.log('🖼️ Image saved to:', path);
      onImageGenerate(path);
    } catch (e) {
      console.error('Error saving image:', e);
    }
  };

  const fetchStepData = async (data: { start: Date; end: Date }) => {
    if (Platform.OS === 'ios') {
      try {
        const results: any[] = [];
        let currentDate = moment(data.start);

        while (currentDate.isSameOrBefore(data.end, 'day')) {
          const dateStr = currentDate.toISOString();

          await new Promise<void>(resolve => {
            AppleHealthKit.getStepCount(
              {
                date: dateStr,
                ascending: true,
                includeManuallyAdded: true,
              },
              (err, result) => {
                if (err) {
                  console.log(`Error getting steps for ${dateStr}:`, err);
                } else {
                  results.push({ date: dateStr, steps: result?.value || 0 });
                }
                resolve();
              },
            );
          });

          currentDate.add(1, 'day');
        }
        console.log('Steps data fetched:', results);
        setStepData(results);
      } catch (error) {
        console.log(error, 'Error fetching step data');
      }
    } else {
      try {
        // Ensure Health Connect is initialized before reading data
        if (!isHealthConnectInitialized) {
          console.log('Health Connect not initialized, attempting to initialize...');
          await initialize();
          setIsHealthConnectInitialized(true);
        }

        const results = await readRecords('Steps', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });
        const formattedResults = results.records.map(item => ({
          date: item.startTime,
          steps: item.count,
        }));
        setStepData(formattedResults);
        console.log('Steps data fetched successfully:', formattedResults.length, 'records');
      } catch (error) {
        console.log('Error fetching steps data:', error);
        setStepData([]); // Set empty array on error

        // Handle specific Health Connect errors
        if (error && typeof error === 'object' && 'message' in error) {
          const errorMessage = (error as Error).message;
          if (errorMessage.includes('not initialized')) {
            console.log('Health Connect client is not initialized');
            setIsHealthConnectInitialized(false);
          } else if (errorMessage.includes('permission')) {
            console.log('Health Connect permission denied');
            setIsConnected(false);
          }
        }
      }
    }
  };

  const fetchCaloriesData = async (data: { start: Date; end: Date }) => {
    if (Platform.OS === 'ios') {
      await new Promise<void>((resolve, reject) => {
        AppleHealthKit.getActiveEnergyBurned(
          {
            startDate: moment(data.start).startOf('day').toISOString(),
            endDate:   moment(data.end).endOf('day').toISOString(),
            ascending: true,
            includeManuallyAdded: true,
          },
          (err, result) => {
            if (err) {
              console.log('Error getting calories:', err);
              return reject(err);
            }
            const calories = result.map(item => ({
              date:    item.startDate,
              calorie: item.value,
            }));
            setCalorieData(calories);
            resolve();
          }
        );
      });
    } else {
      try {
        // Ensure Health Connect is initialized before reading data
        if (!isHealthConnectInitialized) {
          console.log('Health Connect not initialized, attempting to initialize...');
          await initialize();
          setIsHealthConnectInitialized(true);
        }

        const results = await readRecords('TotalCaloriesBurned', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });
        console.log('TotalCaloriesBurned results fetched:', results.records);

        const calories = results.records.map(item => {
          const rawKcal = item.energy.inCalories / 1000;
          return {
            date: item.startTime,
            calorie: Math.round(rawKcal * 100) / 100,
          };
        });
        console.log('Calories data fetched:', calories, 'records');
        setCalorieData(calories);
        console.log('Calories data fetched successfully:', calories.length, 'records');
      } catch (error) {
        console.log('Error fetching calories data:', error);
        setCalorieData([]); // Set empty array on error

        // Handle specific Health Connect errors
        if (error && typeof error === 'object' && 'message' in error) {
          const errorMessage = (error as Error).message;
          if (errorMessage.includes('not initialized')) {
            console.log('Health Connect client is not initialized');
            setIsHealthConnectInitialized(false);
          } else if (errorMessage.includes('permission')) {
            console.log('Health Connect permission denied');
            setIsConnected(false);
          }
        }
      }
    }
  };

  const fetchDistanceData = async (data: { start: Date; end: Date }) => {
    if (Platform.OS === 'ios') {
      try {
        const results: any[] = [];
        let currentDate = moment(data.start);
        while (currentDate.isSameOrBefore(data.end, 'day')) {
          const dateStr = currentDate.toISOString();
          await new Promise<void>(resolve => {
            AppleHealthKit.getDistanceWalkingRunning(
              {
                date: dateStr,
                ascending: true,
                includeManuallyAdded: true,
              },
              (err, result) => {
                if (err) {
                  console.log(`Error getting steps for ${dateStr}:`, err);
                } else {
                  results.push({ date: dateStr, meter: result?.value || 0 });
                }
                resolve();
              },
            );
          });
          currentDate.add(1, 'day');
        }
        setDistanceData(results);
      } catch (error) {
        console.log(error, 'Error fetching step data');
      }
    } else {
      try {
        // Ensure Health Connect is initialized before reading data
        if (!isHealthConnectInitialized) {
          console.log('Health Connect not initialized, attempting to initialize...');
          await initialize();
          setIsHealthConnectInitialized(true);
        }

        // --- NEW ANDROID DISTANCE LOGIC ---
        const exerciseResults = await readRecords('ExerciseSession', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });
        console.log('Exercise results fetched:', exerciseResults.records.length);

        const excludedTypes = [8, 9, 13, 54,12,5,47,48]; // Cycling, Stationary cycling, Rowing, Skiing
        const excludedSessions = exerciseResults.records.filter(session =>
          excludedTypes.includes(session.exerciseType)
        );

        const distanceResults = await readRecords('Distance', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });
        console.log('Distance results fetched:', distanceResults.records.length);

        // Total raw distance
        const totalRawDistance = distanceResults.records.reduce(
          (sum, r) => sum + (r.distance?.inMeters || 0),
          0
        );

        // Distance during excluded sessions
        let excludedDistance = 0;
        for (const session of excludedSessions) {
          const sessionStart = new Date(session.startTime).getTime();
          const sessionEnd = new Date(session.endTime).getTime();

          const matching = distanceResults.records.filter(r => {
            const t = new Date(r.startTime).getTime();
            return t >= sessionStart && t <= sessionEnd;
          });

          const sessionDistance = matching.reduce(
            (sum, r) => sum + (r.distance?.inMeters || 0),
            0
          );

          excludedDistance += sessionDistance;
        }

        const finalDistance = Math.max(totalRawDistance - excludedDistance, 0);

        console.log('Total raw distance:', totalRawDistance);
        console.log('Excluded activity distance:', excludedDistance);
        console.log('Final walking-like distance:', finalDistance);

        setDistanceData([{ date: new Date().toISOString(), meter: finalDistance }]);
        // --- END NEW ANDROID DISTANCE LOGIC ---
      } catch (error) {
        console.log('Error fetching distance data:', error);
        setDistanceData([]); // Set empty array on error

        // Handle specific Health Connect errors
        if (error && typeof error === 'object' && 'message' in error) {
          const errorMessage = (error as Error).message;
          if (errorMessage.includes('not initialized')) {
            console.log('Health Connect client is not initialized');
            setIsHealthConnectInitialized(false);
          } else if (errorMessage.includes('permission')) {
            console.log('Health Connect permission denied');
            setIsConnected(false);
          }
        }
      }
    }
  };

  const fetchCyclingDistanceData = async (data: { start: Date; end: Date }) => {
    if (Platform.OS === 'ios') {
      try {
        const results: any[] = [];
        let currentDate = moment(data.start);

        while (currentDate.isSameOrBefore(data.end, 'day')) {
          const dateStr = currentDate.toISOString();

          await new Promise<void>(resolve => {
            AppleHealthKit.getDistanceCycling(
              {
                date: dateStr,
                ascending: true,
                includeManuallyAdded: true,
              },
              (err, result) => {
                if (err) {
                  console.log(`Error getting cycling distance for ${dateStr}:`, err);
                } else {
                  results.push({ date: dateStr, meter: result?.value || 0 });
                }
                resolve();
              },
            );
          });

          currentDate.add(1, 'day');
        }
        setCyclingDistanceData(results);
      } catch (error) {
        console.log(error, 'Error fetching cycling distance data (iOS)');
      }
    } else {
      try {
        if (!isHealthConnectInitialized) {
          console.log('Health Connect not initialized, attempting to initialize...');
          await initialize();
          setIsHealthConnectInitialized(true);
        }

        const exerciseResults = await readRecords('ExerciseSession', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });
        console.log('Exercise results fetched1:', exerciseResults.records, 'records');

        const cyclingResults = exerciseResults.records.filter(
          item => item.exerciseType === 8 || item.exerciseType === 9, // 8 is the code for CYCLING an 9 is for stationary cycling in Health Connect
        );
        console.log('Cycling results fetched:', cyclingResults, 'records');



        const distanceResults = await readRecords('Distance', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });
        // let totalCyclingDistance = 0;

        // for (const session of cyclingResults) {
        //   const sessionStart = new Date(session.startTime).getTime();
        //   const sessionEnd = new Date(session.endTime).getTime();

        //   const matchingDistances = distanceResults.records.filter(record => {
        //     const recordTime = new Date(record.startTime).getTime();
        //     return recordTime >= sessionStart && recordTime <= sessionEnd;
        //   });

        //   const sessionDistance = matchingDistances.reduce(
        //     (sum, r) => sum + r.distance.inMeters,
        //     0
        //   );

        //   totalCyclingDistance += sessionDistance;
        // }
        // console.log('Distance results fetched:', totalCyclingDistance);

        // Fallback strategy:
        // 1. If total distance is in ExerciseSession, use that
        // 2. Else, aggregate matching Distance records per session
        const formattedFromSessions = cyclingResults.map(session => {
          const sessionStart = new Date(session.startTime).getTime();
          const sessionEnd = new Date(session.endTime).getTime();

          const matchingDistances = distanceResults.records.filter(record => {
            const recordTime = new Date(record.startTime).getTime();
            return recordTime >= sessionStart && recordTime <= sessionEnd;
          });

          const sessionDistance = matchingDistances.reduce(
            (sum, r) => sum + r.distance.inMeters,
            0
          );

          return {
            date: session.startTime,
            meter: sessionDistance,
          };
        });

        setCyclingDistanceData(formattedFromSessions);
        console.log('Cycling distance data (aggregated per session):', formattedFromSessions);

        
      } catch (error) {
        console.log('Error fetching cycling distance data (Android):', error);
        setCyclingDistanceData([]);
        if (error && typeof error === 'object' && 'message' in error) {
          const errorMessage = (error as Error).message;
          if (errorMessage.includes('not initialized')) {
            setIsHealthConnectInitialized(false);
          } else if (errorMessage.includes('permission')) {
            setIsConnected(false);
          }
        }
      }
    }
  };

  // Fetch swimming distance data from Apple Health (iOS) or Health Connect (Android)
  const fetchSwimmingData = async (data: { start: Date; end: Date }) => {
    if (Platform.OS === 'ios') {
      try {
        const results: any[] = [];
        let currentDate = moment(data.start);

        while (currentDate.isSameOrBefore(data.end, 'day')) {
          const dateStr = currentDate.toISOString();

          await new Promise<void>(resolve => {
            AppleHealthKit.getDistanceSwimming(
              {
                date: dateStr,
                unit: (unit === 'IMPERIAL' ? 'mile' : 'meter') as any,
                includeManuallyAdded: true,
              },
              (err, result) => {
                if (err) {
                  console.log(`Error getting swimming distance for ${dateStr}:`, err);
                } else {
                  results.push({ date: dateStr, distance: result?.value || 0 });
                }
                resolve();
              },
            );
          });

          currentDate.add(1, 'day');
        }
        setSwimmingData(results);
        console.log('Swimming data fetched:', results);
      } catch (error) {
        console.log(error, 'Error fetching swimming data (iOS)');
      }
    } else {
      // Android implementation using Health Connect
      try {
        if (!isHealthConnectInitialized) {
          console.log('Health Connect not initialized, attempting to initialize...');
          await initialize();
          setIsHealthConnectInitialized(true);
        }

        const exerciseResults = await readRecords('ExerciseSession', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });

        // Filter for swimming activities (ExerciseType 56 = Swimming)
        const swimmingResults = exerciseResults.records.filter(
          item => item.exerciseType === 56
        );

        const distanceResults = await readRecords('Distance', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });

        const formattedSwimmingData = swimmingResults.map(session => {
          const sessionStart = new Date(session.startTime).getTime();
          const sessionEnd = new Date(session.endTime).getTime();

          const matchingDistances = distanceResults.records.filter(record => {
            const recordTime = new Date(record.startTime).getTime();
            return recordTime >= sessionStart && recordTime <= sessionEnd;
          });

          const sessionDistance = matchingDistances.reduce(
            (sum, r) => sum + (r.distance?.inMeters || 0),
            0
          );

          return {
            date: session.startTime,
            distance: unit === 'IMPERIAL'
              ? Math.round((sessionDistance * 0.000621371) * 100) / 100 // Convert to miles
              : Math.round((sessionDistance / 1000) * 100) / 100, // Convert to km
          };
        });

        setSwimmingData(formattedSwimmingData);
        console.log('Swimming data fetched (Android):', formattedSwimmingData);
      } catch (error) {
        console.log('Error fetching swimming data (Android):', error);
        setSwimmingData([]);
      }
    }
  };

  const fetchWaterSportsData = async (data: { start: Date; end: Date }) => {
    if (Platform.OS === 'ios') {
      try {
        // Get workout samples for water sports activities
        const waterSportsActivities = ['Swimming', 'PaddleSports', 'WaterFitness', 'WaterPolo', 'WaterSports', 'SurfingSports'];
        const allWaterWorkouts: any[] = [];

        for (const activity of waterSportsActivities) {
          await new Promise<void>(resolve => {
            AppleHealthKit.getSamples(
              {
                startDate: moment(data.start).startOf('day').toISOString(),
                endDate: moment(data.end).endOf('day').toISOString(),
                type: 'Workout' as any,
              },
              (err, results) => {
                if (err) {
                  console.log(`Error getting ${activity} workouts:`, err);
                } else if (results) {
                  // Filter for water sports activities
                  const waterWorkouts = results.filter((workout: any) =>
                    waterSportsActivities.includes(workout.activityName)
                  );
                  allWaterWorkouts.push(...waterWorkouts);
                }
                resolve();
              },
            );
          });
        }

        const formattedWaterWorkouts = allWaterWorkouts.map(workout => ({
          date: workout.start,
          distance: workout.distance || 0,
          calories: workout.calories || 0,
          activityName: workout.activityName,
        }));

        setWaterWorkoutData(formattedWaterWorkouts);
        console.log('Water sports data fetched:', formattedWaterWorkouts);
      } catch (error) {
        console.log(error, 'Error fetching water sports data (iOS)');
      }
    } else {
      // Android implementation
      try {
        if (!isHealthConnectInitialized) {
          await initialize();
          setIsHealthConnectInitialized(true);
        }

        const exerciseResults = await readRecords('ExerciseSession', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });

        // Filter for water sports activities
        // ExerciseType: 56 = Swimming, 47 = Paddling, 48 = Rowing, etc.
        const waterSportsTypes = [56, 47, 48]; // Swimming, Paddling, Rowing
        const waterSportsResults = exerciseResults.records.filter(
          item => waterSportsTypes.includes(item.exerciseType)
        );

        const distanceResults = await readRecords('Distance', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });

        const calorieResults = await readRecords('TotalCaloriesBurned', {
          timeRangeFilter: {
            endTime: moment(data.end).endOf('day').toISOString(),
            startTime: moment(data.start).startOf('day').toISOString(),
            operator: 'between',
          },
        });

        const formattedWaterSportsData = waterSportsResults.map(session => {
          const sessionStart = new Date(session.startTime).getTime();
          const sessionEnd = new Date(session.endTime).getTime();

          // Get distance for this session
          const matchingDistances = distanceResults.records.filter(record => {
            const recordTime = new Date(record.startTime).getTime();
            return recordTime >= sessionStart && recordTime <= sessionEnd;
          });

          const sessionDistance = matchingDistances.reduce(
            (sum, r) => sum + (r.distance?.inMeters || 0),
            0
          );

          // Get calories for this session
          const matchingCalories = calorieResults.records.filter(record => {
            const recordTime = new Date(record.startTime).getTime();
            return recordTime >= sessionStart && recordTime <= sessionEnd;
          });

          const sessionCalories = matchingCalories.reduce(
            (sum, r) => sum + (r.energy?.inCalories / 1000 || 0),
            0
          );

          const activityName = session.exerciseType === 56 ? 'Swimming' :
                              session.exerciseType === 47 ? 'Paddling' : 'Water Sports';

          return {
            date: session.startTime,
            distance: unit === 'IMPERIAL'
              ? Math.round((sessionDistance * 0.000621371) * 100) / 100
              : Math.round((sessionDistance / 1000) * 100) / 100,
            calories: Math.round(sessionCalories * 100) / 100,
            activityName,
          };
        });

        setWaterWorkoutData(formattedWaterSportsData);
        console.log('Water sports data fetched (Android):', formattedWaterSportsData);
      } catch (error) {
        console.log('Error fetching water sports data (Android):', error);
        setWaterWorkoutData([]);
      }
    }
  };

  const getAllData = async (data: { start: Date; end: Date }) => {
    setStepData([]);
    setCalorieData([]);
    setDistanceData([]);
    setCyclingDistanceData([]);
    setSwimmingData([]);
    setWaterWorkoutData([]);

    try {
      await Promise.all([
        fetchStepData(data),
        fetchCaloriesData(data),
        fetchDistanceData(data),
        fetchCyclingDistanceData(data),
        fetchSwimmingData(data),
        fetchWaterSportsData(data),
      ]);
      setDataFetched(true);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const requestPermissions = async (data: { start: Date; end: Date }) => {
    if (Platform.OS === 'ios') {
      AppleHealthKit.initHealthKit(healthPermission, (err, result) => {
        if (err) {
          Alert.alert('Permission Denied', 'Could not connect to Apple Health');
          setPermissionLoading(false);
          return;
        }
        console.log(result, 'resultresult');

        getAllData(data);
        setIsConnected(true);
        setPermissionLoading(false);
      });
    } else {
      try {
        // Ensure Health Connect is initialized before requesting permissions
        if (!isHealthConnectInitialized) {
          console.log('Initializing Health Connect before requesting permissions...');
          await initialize();
          setIsHealthConnectInitialized(true);
        }

        // Check already granted permissions and only request new ones
        const alreadyGranted = await getGrantedPermissions();
        const alreadyGrantedTypes = new Set(alreadyGranted.map(p => `${p.recordType}:${p.accessType}`));

        const requestedPermissions: { accessType: 'read'; recordType: string }[] = [
          { accessType: 'read', recordType: 'Steps' },
          { accessType: 'read', recordType: 'ActiveCaloriesBurned' },
          { accessType: 'read', recordType: 'TotalCaloriesBurned' },
          { accessType: 'read', recordType: 'Distance' },
          { accessType: 'read', recordType: 'ExerciseSession' },
        ];
        const permissionsToRequest = requestedPermissions.filter(p =>
          !alreadyGrantedTypes.has(`${p.recordType}:${p.accessType}`)
        );

        if (permissionsToRequest.length > 0) {
          const granted = await requestPermission(permissionsToRequest);
          if (!granted) {
            Alert.alert(
              'Permission Denied',
              'Could not connect to Health Connect. Please grant the necessary permissions in the Health Connect app.',
            );
            setPermissionLoading(false);
            return;
          }
        }
        setIsConnected(true);
        getAllData(data);
        setPermissionLoading(false);
      } catch (error: any) {
        setPermissionLoading(false);
        console.log('Error requesting Health Connect permissions:', error);

        let errorMessage = 'Please install Google Health Connect app.';
        if (error && error.message) {
          if (error.message.includes('not initialized')) {
            errorMessage = 'Health Connect is not properly initialized. Please restart the app and try again.';
            setIsHealthConnectInitialized(false);
          } else if (error.message.includes('not available')) {
            errorMessage = 'Health Connect is not available on this device. Please install Google Health Connect app from the Play Store.';
          }
        }

        Alert.alert('Error', errorMessage);
      }
    }
  };

  const onDateSelect = async (data: { start: Date; end: Date }) => {
    if (!isConnected) {
      setPermissionLoading(true);
      requestPermissions(data);
      return;
    }

    // For Android, ensure Health Connect is initialized before fetching data
    if (Platform.OS === 'android' && !isHealthConnectInitialized) {
      try {
        console.log('Initializing Health Connect before fetching data...');
        await initialize();
        setIsHealthConnectInitialized(true);
      } catch (error) {
        console.log('Failed to initialize Health Connect:', error);
        Alert.alert('Error', 'Failed to initialize Health Connect. Please try again.');
        return;
      }
    }

    setStepData([]);
    setCalorieData([]);
    setDistanceData([]);
    setCyclingDistanceData([]);
    setSwimmingData([]);
    setWaterWorkoutData([]);

    try {
      await Promise.all([
        fetchStepData(data),
        fetchCaloriesData(data),
        fetchDistanceData(data),
        fetchCyclingDistanceData(data),
        fetchSwimmingData(data),
        fetchWaterSportsData(data),
      ]);
      setDataFetched(true);
    } catch (error) {
      console.error('Error fetching data:', error);
      Alert.alert('Error', 'Failed to fetch health data. Please check your Health Connect permissions and try again.');
    }
  };

  return (
    <View style={styles.container}>
      {/* <View style={styles.dateRow}>
        <Text style={styles.title}>Fitness Data</Text> */}
      {/* <TouchableOpacity
        style={styles.generateImageButton}
        onPress={() => {
        console.log('Image generate pressed');
        }}>
                      
          <Text style={acrossAllScreens.H3}>Generate Image</Text>
        </TouchableOpacity> */}
      {/* </View> */}
      <View style={styles.dateRow}>
        <AntDesign
          name="calendar"
          size={20}
          color={'black'}
          style={{
            marginRight: 10,
          }}
        />
        <View style={styles.dateBlock}>
          {/* <Text style={styles.label}>Start Date</Text> */}
          <TouchableOpacity
            onPress={() => {
              if (!disabled) setOpenStart(true);
            }}
            style={styles.dateInput}
            disabled={disabled}>
            <Text style={styles.dateText}>
              {startDate ? startDate.toDateString() : 'Select Start Date'}
            </Text>
          </TouchableOpacity>
        </View>
        <DatePicker
          modal
          open={openStart}
          date={startDate || new Date()}
          mode="date"
          onConfirm={date => {
            setOpenStart(false);
            setStartDate(date);
            // onDateSelect({start: date, end: endDate || date});
          }}
          onCancel={() => setOpenStart(false)}
        />
        <View style={styles.dateBlock}>
          {/* <Text style={styles.label}>End Date</Text> */}
          <TouchableOpacity
            onPress={() => {
              if (!disabled) setOpenEnd(true);
            }}
            style={styles.dateInput}
            disabled={disabled}>
            <Text style={styles.dateText}>
              {endDate ? endDate.toDateString() : ' Select End Date'}
            </Text>
          </TouchableOpacity>
        </View>
        <DatePicker
          modal
          open={openEnd}
          date={endDate || new Date()}
          mode="date"
          onConfirm={date => {
            setOpenEnd(false);
            setEndDate(date);
            // onDateSelect({start: startDate || date, end: date});
          }}
          onCancel={() => setOpenEnd(false)}
        />
      </View>
      {/* {stepData && stepData.length > 0 && (
        <View style={[styles.dateRow, {marginTop: 16}]}>
          <Text style={styles.titleText}>Steps Count: {totalSteps}</Text>
        </View>
      )}
      {calorieData && calorieData.length > 0 && (
        <View style={[styles.dateRow, {marginTop: 16}]}>
          <Text style={styles.titleText}>Calorie in Kcal: {totalCalorie}</Text>
        </View>
      )}
      {distanceData && distanceData.length > 0 && (
        <View style={[styles.dateRow, {marginTop: 16}]}>
          <Text style={styles.titleText}>
            Distance in Meter: {totalDistance}
          </Text>
        </View>
      )} */}
      <Modal visible={permissionLoading} transparent>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0,0,0,0.2)',
          }}>
          <Loader
            visible={permissionLoading}
            text="Waiting for permission..."
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  titleText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
  },
  linkText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: 'bold',
    textDecorationLine: 'underline',
  },
  container: {
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 10,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateBlock: {
    flex: 1,
  },
  label: {
    fontSize: 14,
    color: 'gray',
    marginBottom: 4,
  },
  dateInput: {
    paddingVertical: 6,
  },
  dateText: {
    fontSize: 14,
    color: '#000000',
    fontFamily: 'Helvetica Neue',
    fontWeight: 'bold',
  },
  generateImageButton: {
    backgroundColor: '#C3E7F5',

    height: 30,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
    width: 115,
    borderRadius: 15,
  },
});

export default HealthChallengeDatePicker;
